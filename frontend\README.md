# Frontend

## Overview
This frontend is a React application built with TypeScript. It uses Redux for state management and React Router for client-side routing. The project is scaffolded with Vite, a fast build tool for modern web projects.

## Tech Stack
- React
- TypeScript
- Redux (with Redux Toolkit)
- React Router
- Vite

## Folder Structure
- `src/`: Contains source code including components, pages, store, interfaces, and utilities.
- `src/components/`: Reusable UI components.
- `src/pages/`: Page components representing different routes.
- `src/store/`: Redux store setup and slices for state management.
- `src/interfaces/`: TypeScript interfaces and types.
- `src/utils/`: Utility functions such as API helpers.
- `public/`: Static assets and the main HTML file.

## Installation and Running
1. Install dependencies:
   ```bash
   npm install
   ```
2. Run the development server:
   ```bash
   npm run dev
   ```
3. Open your browser at the URL shown in the terminal (usually http://localhost:3000).

---

# Frontend (Tiếng Việt)

## Tổng quan
Frontend này là một ứng dụng React được xây dựng bằng TypeScript. <PERSON><PERSON> sử dụng Redux để quản lý trạng thái và React Router để điều hướng phía client. Dự án được tạo bằng Vite, một công cụ build nhanh cho các dự án web hiện đại.

## Công nghệ sử dụng
- React
- TypeScript
- Redux (với Redux Toolkit)
- React Router
- Vite

## Cấu trúc thư mục
- `src/`: Chứa mã nguồn bao gồm components, pages, store, interfaces và utilities.
- `src/components/`: Các component UI tái sử dụng.
- `src/pages/`: Các component trang đại diện cho các route khác nhau.
- `src/store/`: Cấu hình Redux store và các slices để quản lý trạng thái.
- `src/interfaces/`: Các interface và kiểu dữ liệu TypeScript.
- `src/utils/`: Các hàm tiện ích như helper cho API.
- `public/`: Tài nguyên tĩnh và file HTML chính.

## Cài đặt và chạy
1. Cài đặt các thư viện phụ thuộc:
   ```bash
   npm install
   ```
2. Chạy server phát triển:
   ```bash
   npm run dev
   ```
3. Mở trình duyệt tại URL hiển thị trong terminal (thường là http://localhost:3000).
