# Frontend Architecture

This frontend follows the FastAPI full-stack template structure with modern React patterns.

## 📁 Folder Structure

```
src/
├── client/           # API client and types
│   ├── api.ts       # Axios client configuration
│   ├── services.ts  # API service functions
│   ├── types.ts     # TypeScript types and interfaces
│   └── index.ts     # Exports
├── components/       # Reusable UI components
│   ├── common/      # Generic components
│   │   ├── LoadingSpinner.tsx
│   │   ├── ErrorAlert.tsx
│   │   ├── DataTable.tsx
│   │   └── ConfirmDialog.tsx
│   ├── Navbar.tsx   # Layout components
│   └── index.ts     # Exports
├── hooks/           # Custom React hooks
│   ├── useApi.ts    # Generic API hooks
│   ├── useProducts.ts # Entity-specific hooks
│   └── index.ts     # Exports
├── pages/           # Page components
│   ├── Dashboard.tsx
│   ├── Products.tsx
│   ├── Categories.tsx
│   └── ...
├── routes/          # Route configuration
│   └── index.tsx    # Route definitions
├── store/           # Redux store (legacy)
│   └── ...
├── utils/           # Utility functions
│   ├── format.ts    # Formatting utilities
│   ├── validation.ts # Form validation
│   └── index.ts     # Exports
└── App.tsx          # Main app component
```

## 🏗️ Architecture Patterns

### 1. API Client Layer (`client/`)

**Purpose**: Centralized API communication with type safety.

**Key Files**:
- `api.ts`: Axios client with interceptors for auth and error handling
- `services.ts`: Service functions for each entity (products, categories, etc.)
- `types.ts`: TypeScript interfaces for API requests/responses

**Example Usage**:
```typescript
import { productsService } from '../client/services'
import type { Product, ProductCreate } from '../client/types'

// Fetch products
const response = await productsService.getAll({ page: 1, pageSize: 10 })

// Create product
const newProduct = await productsService.create(productData)
```

### 2. Custom Hooks (`hooks/`)

**Purpose**: Encapsulate API logic and state management in reusable hooks.

**Key Patterns**:
- `useApi`: Generic hook for GET requests
- `useMutation`: Generic hook for POST/PUT/DELETE requests
- `usePaginatedApi`: Hook for paginated data
- Entity-specific hooks: `useProducts`, `useCategories`, etc.

**Example Usage**:
```typescript
import { useProducts, useProductManagement } from '../hooks'

function ProductsPage() {
  const { data, loading, error, changePage } = useProducts()
  const { create, update, delete: deleteProduct } = useProductManagement()
  
  // Component logic...
}
```

### 3. Reusable Components (`components/`)

**Purpose**: UI components that can be used across multiple pages.

**Key Components**:
- `LoadingSpinner`: Consistent loading states
- `ErrorAlert`: Error display with retry functionality
- `DataTable`: Enhanced Ant Design table with pagination
- `ConfirmDialog`: Confirmation dialogs for actions

**Example Usage**:
```typescript
import { DataTable, showDeleteConfirm } from '../components'

const columns = [
  { key: 'name', title: 'Name', dataIndex: 'name' },
  // ...
]

<DataTable
  data={products}
  columns={columns}
  loading={loading}
  pagination={pagination}
/>
```

### 4. Utilities (`utils/`)

**Purpose**: Helper functions for formatting, validation, and common operations.

**Key Files**:
- `format.ts`: Date, currency, text formatting
- `validation.ts`: Form validation rules
- `index.ts`: Common utilities (debounce, throttle, etc.)

**Example Usage**:
```typescript
import { formatCurrency, formatDate, commonRules } from '../utils'

const price = formatCurrency(29.99) // "$29.99"
const date = formatDate(new Date()) // "Dec 15, 2024"

// In forms
<Form.Item name="name" rules={commonRules.name}>
  <Input />
</Form.Item>
```

## 🔄 Migration Strategy

### Phase 1: New Structure Setup ✅
- [x] Create `client/` folder with API services and types
- [x] Create `hooks/` folder with custom hooks
- [x] Create `components/` folder with reusable components
- [x] Create `utils/` folder with helper functions
- [x] Update routing structure

### Phase 2: Gradual Migration
- [ ] Migrate Categories page to new structure (example: `CategoriesNew.tsx`)
- [ ] Migrate Products page
- [ ] Migrate other pages one by one
- [ ] Update existing components to use new patterns

### Phase 3: Cleanup
- [ ] Remove Redux store (replace with React Query or Zustand if needed)
- [ ] Remove old API utilities
- [ ] Update all imports to use new structure

## 🎯 Benefits of New Structure

1. **Type Safety**: Full TypeScript support with proper API types
2. **Reusability**: Components and hooks can be reused across pages
3. **Maintainability**: Clear separation of concerns
4. **Testability**: Easier to unit test individual components and hooks
5. **Performance**: Better caching and state management with custom hooks
6. **Developer Experience**: Consistent patterns and better IntelliSense

## 📝 Code Examples

### Creating a New Page

```typescript
import React, { useState } from 'react'
import { LoadingSpinner, ErrorAlert, DataTable } from '../components'
import { useApi, useMutation } from '../hooks'
import { myEntityService } from '../client/services'
import type { MyEntity, MyEntityCreate } from '../client/types'

const MyEntityPage: React.FC = () => {
  const { data, loading, error, execute: refetch } = useApi(() => myEntityService.getAll())
  const createMutation = useMutation(
    (data: MyEntityCreate) => myEntityService.create(data),
    { onSuccess: () => refetch() }
  )

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorAlert description={error} onRetry={refetch} />

  return (
    <div>
      {/* Page content */}
    </div>
  )
}
```

### Adding a New API Service

```typescript
// In client/services.ts
export const myEntityService = {
  getAll: () => apiClient.get<ApiResponse<MyEntity[]>>('/my-entities'),
  getById: (id: string) => apiClient.get<MyEntity>(`/my-entities/${id}`),
  create: (data: MyEntityCreate) => apiClient.post<MyEntity>('/my-entities', data),
  update: (id: string, data: MyEntityUpdate) => apiClient.put<MyEntity>(`/my-entities/${id}`, data),
  delete: (id: string) => apiClient.delete(`/my-entities/${id}`),
}
```

## 🚀 Getting Started

1. **For new pages**: Use `CategoriesNew.tsx` as a template
2. **For API calls**: Use services from `client/services.ts`
3. **For state management**: Use custom hooks from `hooks/`
4. **For UI components**: Use components from `components/`
5. **For utilities**: Use functions from `utils/`

This structure provides a solid foundation for scalable React applications following modern best practices.
