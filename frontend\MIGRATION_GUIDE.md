# Frontend Migration Guide

## 🎯 Overview

This guide outlines the migration from the current Redux-based structure to a modern FastAPI full-stack template inspired architecture.

## ✅ Completed Changes

### 1. New Folder Structure Created

```
src/
├── client/           # ✅ NEW: API client and types
├── components/       # ✅ ENHANCED: Reusable components
├── hooks/           # ✅ NEW: Custom React hooks
├── routes/          # ✅ NEW: Route configuration
├── utils/           # ✅ ENHANCED: Utility functions
├── pages/           # ✅ EXISTING: Page components
└── store/           # 🔄 LEGACY: To be phased out
```

### 2. API Client Layer (`client/`)

- **`api.ts`**: Centralized Axios client with interceptors
- **`services.ts`**: Service functions for all entities
- **`types.ts`**: Complete TypeScript type definitions
- **`index.ts`**: Clean exports

### 3. Custom Hooks (`hooks/`)

- **`useApi.ts`**: Generic hooks for API calls
- **`useProducts.ts`**: Example entity-specific hooks
- **`index.ts`**: Hook exports

### 4. Enhanced Components (`components/`)

- **`LoadingSpinner.tsx`**: Consistent loading states
- **`ErrorAlert.tsx`**: Error handling with retry
- **`DataTable.tsx`**: Enhanced table component
- **`ConfirmDialog.tsx`**: Confirmation dialogs

### 5. Utility Functions (`utils/`)

- **`format.ts`**: Date, currency, text formatting
- **`validation.ts`**: Form validation rules
- **`index.ts`**: Common utilities

### 6. Route Configuration (`routes/`)

- **`index.tsx`**: Centralized route definitions

## 🔄 Migration Status

### ✅ Phase 1: Infrastructure (COMPLETED)
- [x] Create new folder structure
- [x] Set up API client layer
- [x] Create custom hooks
- [x] Build reusable components
- [x] Add utility functions
- [x] Update routing

### 🚧 Phase 2: Page Migration (IN PROGRESS)
- [x] Create example page (`CategoriesNew.tsx`)
- [ ] Migrate Categories page
- [ ] Migrate Products page
- [ ] Migrate Orders page
- [ ] Migrate Customers page
- [ ] Migrate Stores page
- [ ] Migrate Dashboard page

### ⏳ Phase 3: Cleanup (PENDING)
- [ ] Remove Redux dependencies
- [ ] Clean up old API utilities
- [ ] Update all imports
- [ ] Remove legacy code

## 📋 How to Use New Structure

### 1. Creating a New Page

Use `CategoriesNew.tsx` as a template:

```typescript
import React, { useState } from 'react'
import { LoadingSpinner, ErrorAlert, DataTable } from '../components'
import { useApi, useMutation } from '../hooks'
import { myEntityService } from '../client/services'

const MyPage: React.FC = () => {
  const { data, loading, error, execute: refetch } = useApi(() => myEntityService.getAll())
  
  // Component logic...
}
```

### 2. API Calls

```typescript
// Old way (Redux)
import { fetchProducts } from '../store/slices/productsSlice'
dispatch(fetchProducts())

// New way (Hooks)
import { useProducts } from '../hooks'
const { data, loading, error } = useProducts()
```

### 3. Form Validation

```typescript
// Old way
rules={[{ required: true, message: 'Required!' }]}

// New way
import { commonRules } from '../utils'
rules={commonRules.name}
```

### 4. Formatting

```typescript
// Old way
const formatted = `$${price.toFixed(2)}`

// New way
import { formatCurrency } from '../utils'
const formatted = formatCurrency(price)
```

## 🔧 Migration Steps for Each Page

### Step 1: Update Imports
```typescript
// Replace Redux imports
- import { useSelector, useDispatch } from 'react-redux'
- import { fetchData } from '../store/slices/dataSlice'

// With new imports
+ import { useApi, useMutation } from '../hooks'
+ import { dataService } from '../client/services'
+ import { LoadingSpinner, ErrorAlert } from '../components'
```

### Step 2: Replace Redux Logic
```typescript
// Old Redux pattern
const dispatch = useDispatch()
const { items, loading, error } = useSelector(state => state.data)

useEffect(() => {
  dispatch(fetchData())
}, [dispatch])

// New hook pattern
const { data, loading, error, execute: refetch } = useApi(() => dataService.getAll())
```

### Step 3: Update CRUD Operations
```typescript
// Old way
const handleCreate = async (values) => {
  try {
    await dispatch(createItem(values)).unwrap()
    message.success('Created!')
    dispatch(fetchItems())
  } catch (error) {
    message.error('Failed!')
  }
}

// New way
const createMutation = useMutation(
  (data) => dataService.create(data),
  { onSuccess: () => refetch() }
)

const handleCreate = (values) => createMutation.mutate(values)
```

### Step 4: Update Components
```typescript
// Replace loading/error handling
- if (loading) return <Spin />
- if (error) return <Alert />

+ if (loading) return <LoadingSpinner />
+ if (error) return <ErrorAlert description={error} onRetry={refetch} />
```

## 🎯 Benefits After Migration

1. **Type Safety**: Full TypeScript support
2. **Better Performance**: Optimized API calls and caching
3. **Cleaner Code**: Less boilerplate, more readable
4. **Reusability**: Components and hooks can be shared
5. **Maintainability**: Clear separation of concerns
6. **Developer Experience**: Better IntelliSense and debugging

## 📚 Examples

### Before (Redux)
```typescript
const Products: React.FC = () => {
  const dispatch = useDispatch()
  const { items, loading, error } = useSelector(state => state.products)
  
  useEffect(() => {
    dispatch(fetchProducts())
  }, [dispatch])
  
  const handleDelete = async (id) => {
    try {
      await dispatch(deleteProduct(id)).unwrap()
      message.success('Deleted!')
      dispatch(fetchProducts())
    } catch (error) {
      message.error('Failed!')
    }
  }
  
  if (loading) return <Spin />
  if (error) return <Alert message={error} />
  
  return <div>{/* Component JSX */}</div>
}
```

### After (Hooks)
```typescript
const Products: React.FC = () => {
  const { data: products, loading, error, execute: refetch } = useApi(() => productsService.getAll())
  const deleteMutation = useMutation(
    (id) => productsService.delete(id),
    { onSuccess: () => refetch() }
  )
  
  const handleDelete = (product) => {
    showDeleteConfirm(product.name, () => deleteMutation.mutate(product.id))
  }
  
  if (loading) return <LoadingSpinner />
  if (error) return <ErrorAlert description={error} onRetry={refetch} />
  
  return <div>{/* Component JSX */}</div>
}
```

## 🚀 Next Steps

1. **Review** the new structure in `CategoriesNew.tsx`
2. **Migrate** one page at a time using the patterns shown
3. **Test** each migrated page thoroughly
4. **Remove** old Redux code once all pages are migrated
5. **Optimize** performance and add additional features as needed

The new structure provides a solid foundation for scalable React applications following modern best practices inspired by the FastAPI full-stack template.
