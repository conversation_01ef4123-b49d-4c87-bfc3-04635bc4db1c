[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "fastapi-backend"
version = "0.1.0"
description = "Backend for FastAPI demo application"
requires-python = ">=3.10,<4.0"

dependencies = [
    "fastapi[standard]>=0.114.2,<1.0.0",
    "python-multipart>=0.0.7,<1.0.0",
    "email-validator>=2.1.0.post1,<*******",
    "passlib[bcrypt]>=1.7.4,<2.0.0",
    "tenacity>=8.2.3,<9.0.0",
    "pydantic>2.0",
    "emails>=0.6,<1.0",
    "jinja2>=3.1.4,<4.0.0",
    "alembic>=1.12.1,<2.0.0",
    "httpx>=0.25.1,<1.0.0",
    "psycopg[binary]>=3.1.13,<4.0.0",
    "sqlmodel>=0.0.21,<1.0.0",
    "bcrypt==4.0.1",
    "pydantic-settings>=2.2.1,<3.0.0",
    "sentry-sdk[fastapi]>=1.40.6,<2.0.0",
    "pyjwt>=2.8.0,<3.0.0",
]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3,<8.0.0",
    "mypy>=1.8.0,<2.0.0",
    "ruff>=0.2.2,<1.0.0",
    "pre-commit>=3.6.2,<4.0.0",
    "types-passlib>=1.7.7.20240106,<*******",
    "coverage>=7.4.3,<8.0.0",
]
