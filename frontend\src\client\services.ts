import { apiClient } from './api'
import type {
  Product,
  ProductCreate,
  ProductUpdate,
  ProductsParams,
  Category,
  CategoryCreate,
  CategoryUpdate,
  Store,
  StoreCreate,
  StoreUpdate,
  Customer,
  CustomerCreate,
  CustomerUpdate,
  Order,
  OrderCreate,
  OrderUpdate,
  Variant,
  VariantCreate,
  VariantUpdate,
  VariantsParams,
  OrderDetail,
  OrderDetailCreate,
  OrderDetailUpdate,
  OrderDetailsParams,
  ApiResponse,
  PaginatedResponse,
} from './types'

// Products Service
export const productsService = {
  getAll: (params?: ProductsParams) =>
    apiClient.get<PaginatedResponse<Product>>('/api/products', params),

  getById: (id: string) =>
    apiClient.get<Product>(`/api/products/${id}`),

  create: (data: ProductCreate) =>
    apiClient.post<Product>('/api/products', data),

  update: (id: string, data: ProductUpdate) =>
    apiClient.put<Product>(`/api/products/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/products/${id}`),
}

// Categories Service
export const categoriesService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Category>>('/api/categories'),

  getById: (id: string) =>
    apiClient.get<Category>(`/api/categories/${id}`),

  create: (data: CategoryCreate) =>
    apiClient.post<Category>('/api/categories', data),

  update: (id: string, data: CategoryUpdate) =>
    apiClient.put<Category>(`/api/categories/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/categories/${id}`),
}

// Stores Service
export const storesService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Store>>('/api/stores'),

  getById: (id: string) =>
    apiClient.get<Store>(`/api/stores/${id}`),

  create: (data: StoreCreate) =>
    apiClient.post<Store>('/api/stores', data),

  update: (id: string, data: StoreUpdate) =>
    apiClient.put<Store>(`/api/stores/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/stores/${id}`),
}

// Customers Service
export const customersService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Customer>>('/api/customers'),

  getById: (id: string) =>
    apiClient.get<Customer>(`/api/customers/${id}`),

  create: (data: CustomerCreate) =>
    apiClient.post<Customer>('/api/customers', data),

  update: (id: string, data: CustomerUpdate) =>
    apiClient.put<Customer>(`/api/customers/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/customers/${id}`),
}

// Orders Service
export const ordersService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Order>>('/api/orders'),

  getById: (id: string) =>
    apiClient.get<Order>(`/api/orders/${id}`),

  create: (data: OrderCreate) =>
    apiClient.post<Order>('/api/orders', data),

  update: (id: string, data: OrderUpdate) =>
    apiClient.put<Order>(`/api/orders/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/orders/${id}`),
}

// Variants Service
export const variantsService = {
  getAll: (params?: VariantsParams) =>
    apiClient.get<PaginatedResponse<Variant>>('/api/variants', params),

  getByProduct: (productId: string) =>
    apiClient.get<PaginatedResponse<Variant>>('/api/variants', { product_id: productId }),

  getById: (id: string) =>
    apiClient.get<Variant>(`/api/variants/${id}`),

  create: (data: VariantCreate) =>
    apiClient.post<Variant>('/api/variants', data),

  update: (id: string, data: VariantUpdate) =>
    apiClient.put<Variant>(`/api/variants/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/variants/${id}`),
}

// Order Details Service
export const orderDetailsService = {
  getAll: (params?: OrderDetailsParams) =>
    apiClient.get<PaginatedResponse<OrderDetail>>('/api/order-details', params),

  getById: (id: string) =>
    apiClient.get<OrderDetail>(`/api/order-details/${id}`),

  create: (data: OrderDetailCreate) =>
    apiClient.post<OrderDetail>('/api/order-details', data),

  update: (id: string, data: OrderDetailUpdate) =>
    apiClient.put<OrderDetail>(`/api/order-details/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/order-details/${id}`),
}

// Export all services
export const services = {
  products: productsService,
  categories: categoriesService,
  stores: storesService,
  customers: customersService,
  orders: ordersService,
  variants: variantsService,
  orderDetails: orderDetailsService,
}

export default services
