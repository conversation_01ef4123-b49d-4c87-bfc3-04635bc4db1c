import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Form, 
  Select, 
  InputNumber, 
  Typography, 
  Space, 
  Descriptions,
  Tag,
  Statistic
} from 'antd'
import { 
  ArrowLeftOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons'
import { 
  LoadingSpinner, 
  ErrorAlert, 
  DataTable,
  showDeleteConfirm, 
  showUpdateConfirm 
} from '../components'
import { useApi, useMutation } from '../hooks'
import { 
  ordersService, 
  orderDetailsService, 
  variantsService,
  customersService,
  storesService
} from '../client/services'
import type { 
  Order, 
  OrderDetail, 
  OrderDetailCreate, 
  OrderDetailUpdate, 
  TableColumn 
} from '../client/types'
import { formatDate, formatId, formatCurrency, formatQuantity, commonRules } from '../utils'

const { Title, Text } = Typography
const { Option } = Select

const OrderDetailPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>()
  const navigate = useNavigate()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
  const [editingOrderDetail, setEditingOrderDetail] = useState<OrderDetail | null>(null)
  const [form] = Form.useForm()

  // Fetch order details
  const { 
    data: order, 
    loading: orderLoading, 
    error: orderError 
  } = useApi(() => ordersService.getById(orderId!), { immediate: !!orderId })

  // Fetch order details (items)
  const { 
    data: orderDetailsResponse, 
    loading: orderDetailsLoading, 
    error: orderDetailsError,
    execute: refetchOrderDetails 
  } = useApi(() => orderDetailsService.getByOrder(orderId!), { immediate: !!orderId })

  // Fetch variants for dropdown
  const { data: variantsResponse } = useApi(() => variantsService.getAll())

  // Fetch customers and stores for order info
  const { data: customersResponse } = useApi(() => customersService.getAll())
  const { data: storesResponse } = useApi(() => storesService.getAll())

  // Mutations
  const createOrderDetailMutation = useMutation(
    (data: OrderDetailCreate) => orderDetailsService.create(data),
    { onSuccess: () => { refetchOrderDetails(); setIsModalVisible(false); form.resetFields() } }
  )

  const updateOrderDetailMutation = useMutation(
    ({ id, data }: { id: string; data: OrderDetailUpdate }) => orderDetailsService.update(id, data),
    { onSuccess: () => { refetchOrderDetails(); setIsModalVisible(false); form.resetFields() } }
  )

  const deleteOrderDetailMutation = useMutation(
    (id: string) => orderDetailsService.delete(id),
    { onSuccess: () => refetchOrderDetails() }
  )

  const orderDetails = orderDetailsResponse?.data || []
  const variants = variantsResponse?.data || []
  const customers = customersResponse?.data || []
  const stores = storesResponse?.data || []

  // Helper functions
  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    return customer?.name || customer?.username || 'Unknown Customer'
  }

  const getStoreName = (storeId: string) => {
    const store = stores.find(s => s.id === storeId)
    return store?.name || store?.name_store || 'Unknown Store'
  }

  const getVariantInfo = (variantId: string) => {
    const variant = variants.find(v => v.id === variantId)
    return variant || null
  }

  const calculateTotal = () => {
    return orderDetails.reduce((total, item) => {
      return total + (item.price || 0)
    }, 0)
  }

  const calculateItemCount = () => {
    return orderDetails.reduce((total, item) => {
      return total + (item.quantity || 0)
    }, 0)
  }

  // Handlers
  const showAddModal = () => {
    setModalMode('add')
    setEditingOrderDetail(null)
    form.resetFields()
    form.setFieldsValue({ order_id: orderId })
    setIsModalVisible(true)
  }

  const showEditModal = (orderDetail: OrderDetail) => {
    setModalMode('edit')
    setEditingOrderDetail(orderDetail)
    form.setFieldsValue({
      order_id: orderDetail.order_id,
      variant_id: orderDetail.variant_id,
      quantity: orderDetail.quantity,
      unit_price: orderDetail.unit_price,
    })
    setIsModalVisible(true)
  }

  const handleDeleteOrderDetail = (orderDetail: OrderDetail) => {
    const variant = getVariantInfo(orderDetail.variant_id)
    const itemName = variant?.beverage_option || `Item ${formatId(orderDetail.id)}`
    showDeleteConfirm(itemName, () => deleteOrderDetailMutation.mutate(orderDetail.id))
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalVisible(false)
  }

  const onFinish = async (values: any) => {
    const orderDetailData = {
      ...values,
      price: (values.quantity || 0) * (values.unit_price || 0), // Calculate total price
    }

    if (modalMode === 'add') {
      createOrderDetailMutation.mutate(orderDetailData as OrderDetailCreate)
    } else if (modalMode === 'edit' && editingOrderDetail) {
      const variant = getVariantInfo(values.variant_id)
      const itemName = variant?.beverage_option || `Item ${formatId(editingOrderDetail.id)}`
      showUpdateConfirm(itemName, () => {
        updateOrderDetailMutation.mutate({ id: editingOrderDetail.id, data: orderDetailData })
      })
    }
  }

  // Table columns for order details
  const columns: TableColumn<OrderDetail>[] = [
    {
      key: 'variant',
      title: 'Item',
      render: (_, record: OrderDetail) => {
        const variant = getVariantInfo(record.variant_id)
        return variant?.beverage_option || 'Unknown Item'
      },
      sorter: true,
    },
    {
      key: 'quantity',
      title: 'Quantity',
      dataIndex: 'quantity',
      render: (quantity: number) => formatQuantity(quantity),
      sorter: true,
    },
    {
      key: 'unit_price',
      title: 'Unit Price',
      dataIndex: 'unit_price',
      render: (price: number) => price ? formatCurrency(price) : 'Not set',
      sorter: true,
    },
    {
      key: 'price',
      title: 'Total Price',
      dataIndex: 'price',
      render: (price: number) => price ? formatCurrency(price) : 'Not set',
      sorter: true,
    },
    {
      key: 'rate',
      title: 'Rating',
      dataIndex: 'rate',
      render: (rate: number) => rate ? `${rate}/5` : 'Not rated',
    },
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      render: (id: string) => formatId(id),
      width: 100,
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record: OrderDetail) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            size="small"
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteOrderDetail(record)}
            size="small"
          >
            Delete
          </Button>
        </Space>
      ),
      width: 120,
    },
  ]

  if (orderLoading || orderDetailsLoading) return <LoadingSpinner tip="Loading order details..." />
  if (orderError) return <ErrorAlert description={orderError} onRetry={() => navigate('/orders')} />
  if (!order) return <ErrorAlert message="Order not found" onRetry={() => navigate('/orders')} />

  return (
    <>
      <div style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/orders')}
          style={{ marginBottom: 16 }}
        >
          Back to Orders
        </Button>
        <Title level={2}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Order Details
        </Title>
        <Text type="secondary">Order items and information</Text>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={8}>
          <Card title="Order Information">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Customer">
                {getCustomerName(order.customer_id)}
              </Descriptions.Item>
              <Descriptions.Item label="Store">
                {getStoreName(order.store_id)}
              </Descriptions.Item>
              <Descriptions.Item label="Order Date">
                {order.order_date ? formatDate(order.order_date) : 'Not set'}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color="processing">Processing</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Order ID">
                {formatId(order.id)}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic 
                    title="Total Items" 
                    value={calculateItemCount()} 
                    suffix="items"
                  />
                </Col>
                <Col span={12}>
                  <Statistic 
                    title="Total Amount" 
                    value={calculateTotal()} 
                    precision={2}
                    prefix="$"
                  />
                </Col>
              </Row>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <Card 
            title="Order Items" 
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={showAddModal}
                loading={createOrderDetailMutation.loading}
              >
                Add Item
              </Button>
            }
          >
            <DataTable
              data={orderDetails}
              columns={columns}
              loading={orderDetailsLoading || deleteOrderDetailMutation.loading}
              emptyText="No items in this order. Click 'Add Item' to add products to this order."
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title={modalMode === 'add' ? 'Add Order Item' : 'Edit Order Item'}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        okText={modalMode === 'add' ? 'Add' : 'Update'}
        confirmLoading={createOrderDetailMutation.loading || updateOrderDetailMutation.loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ 
            order_id: orderId,
            variant_id: '', 
            quantity: 1, 
            unit_price: undefined
          }}
        >
          <Form.Item name="order_id" style={{ display: 'none' }}>
            <Input />
          </Form.Item>

          <Form.Item
            name="variant_id"
            label="Product Variant"
            rules={[{ required: true, message: 'Please select a product variant!' }]}
          >
            <Select placeholder="Select a product variant" showSearch optionFilterProp="children">
              {variants.map((variant) => (
                <Option key={variant.id} value={variant.id}>
                  {variant.beverage_option || 'Default'} 
                  {variant.price && ` - ${formatCurrency(variant.price)}`}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="Quantity"
                rules={commonRules.quantity}
              >
                <InputNumber 
                  placeholder="1" 
                  style={{ width: '100%' }}
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit_price"
                label="Unit Price ($)"
                rules={commonRules.price}
              >
                <InputNumber 
                  placeholder="0.00" 
                  style={{ width: '100%' }}
                  min={0}
                  step={0.01}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  )
}

export default OrderDetailPage
